#!/bin/bash

# 脚本: restart.sh
# 功能: 执行git pull，检查端口占用，杀死旧进程，后台启动新进程
# 作者: wenhaofree

# 定义变量
APP_DIR="/home/<USER>/github/personal-palette-prototype"
LOG_FILE="$APP_DIR/restart.log"
PORT=4173  # Vite preview服务器的默认端口
BUILD_CMD="pnpm run build"
PREVIEW_CMD="pnpm run preview"

# 确保日志文件存在
touch "$LOG_FILE"

# 输出时间戳的日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 切换到应用目录
cd "$APP_DIR" || {
    log "错误: 无法进入目录 $APP_DIR"
    exit 1
}

# 获取最新代码
log "正在从Git仓库拉取最新代码..."
git pull || {
    log "错误: Git pull失败"
    exit 1
}
log "Git pull完成"

# 检查端口占用并杀死进程
check_and_kill_process() {
    log "检查端口 $PORT 占用情况..."
    # 查找使用该端口的进程PID
    local pid=$(lsof -t -i:$PORT)
    
    if [ -n "$pid" ]; then
        log "发现端口 $PORT 被进程 $pid 占用，正在尝试终止..."
        kill -15 "$pid" 2>/dev/null || {
            log "使用SIGTERM终止进程失败，尝试强制终止..."
            kill -9 "$pid" 2>/dev/null || {
                log "错误: 无法终止进程 $pid"
                return 1
            }
        }
        # 等待进程终止
        for i in {1..5}; do
            if ! lsof -t -i:$PORT &>/dev/null; then
                log "进程已成功终止"
                return 0
            fi
            log "等待进程终止... ($i/5)"
            sleep 1
        done
        
        if lsof -t -i:$PORT &>/dev/null; then
            log "错误: 端口 $PORT 仍被占用"
            return 1
        fi
    else
        log "端口 $PORT 未被占用"
    fi
    return 0
}

# 后台启动应用
start_app() {
    log "安装依赖..."
    pnpm install || {
        log "错误: 依赖安装失败"
        return 1
    }
    
    log "构建生产环境应用..."
    $BUILD_CMD || {
        log "错误: 应用构建失败"
        return 1
    }
    log "应用构建成功"
    
    log "正在后台启动应用预览服务器..."
    nohup $PREVIEW_CMD > "$APP_DIR/nohup.out" 2>&1 &
    
    # 保存进程ID
    echo $! > "$APP_DIR/app.pid"
    log "应用预览服务器启动成功，进程ID: $!"
    
    # 等待几秒，检查进程是否存在
    sleep 3
    if ps -p "$(cat "$APP_DIR/app.pid")" &>/dev/null; then
        log "应用正在运行中"
        return 0
    else
        log "错误: 应用启动失败"
        return 1
    fi
}

# 主流程
main() {
    log "===== 开始重启流程 ====="
    
    # 检查和杀死旧进程
    check_and_kill_process || {
        log "错误: 无法释放端口 $PORT"
        exit 1
    }
    
    # 启动应用
    start_app || {
        log "错误: 应用启动失败"
        exit 1
    }
    
    log "===== 重启流程完成 ====="
}

# 执行主函数
main
