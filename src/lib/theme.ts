// Theme file

// 定义主题类型
export type Theme = "system" | "light" | "dark" | "wenhao"

// 获取存储的主题
export function getThemeFromStorage(): Theme {
  if (typeof window !== 'undefined') {
    const storedTheme = localStorage.getItem('theme') as Theme
    return storedTheme || 'wenhao'
  }
  return 'wenhao'
}

// 保存主题到存储
export function setThemeToStorage(theme: Theme) {
  if (typeof window !== 'undefined') {
    localStorage.setItem('theme', theme)
    document.documentElement.setAttribute('data-theme', theme)
  }
}

// 应用主题
export function applyTheme(theme: Theme) {
  const root = window.document.documentElement
  const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  
  // 移除所有主题类
  root.classList.remove('light', 'dark', 'wenhao')
  
  // 应用新主题
  if (theme === 'system') {
    root.classList.add(systemTheme)
    root.style.colorScheme = systemTheme
  } else {
    root.classList.add(theme)
    root.style.colorScheme = theme === 'wenhao' ? 'wenhao' : 'light'
  }
} 