
#root {
  max-width: 1280px;
  margin: 0 auto;
  text-align: center;
}

/* 基础样式 */
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

/* 全局滚动样式 */
html {
  scroll-behavior: smooth;
}

/* 文本渐变效果 */
.text-gradient {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: text-shimmer 3s ease-in-out infinite alternate;
}

/* 添加新的动画效果 */
@keyframes text-shimmer {
  0% {
    background-position: -100%;
  }
  100% {
    background-position: 200%;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes slide-up-fade-in {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes timeline-pulse {
  0%, 100% {
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
}

/* 应用动画类 */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.floating-element {
  animation: float 6s ease-in-out infinite;
}

.pulse-glow {
  animation: pulse-glow 4s ease-in-out infinite;
}

.rotate-slow {
  animation: rotate-slow 12s linear infinite;
}

.slide-up-fade-in {
  animation: slide-up-fade-in 0.8s ease-out forwards;
}

.timeline-pulse {
  animation: timeline-pulse 3s infinite;
}

/* 鼠标悬停特效 */
.card-pop {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-pop:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.shine {
  position: relative;
  overflow: hidden;
}

.shine::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
  transform: rotate(30deg);
  transition: transform 1s ease-in-out;
  pointer-events: none;
  opacity: 0;
}

.shine:hover::after {
  opacity: 1;
  transform: rotate(30deg) translate(100%, 100%);
}

/* 部分布局样式 */
.section-padding {
  padding: 4rem 0;
}

@media (min-width: 768px) {
  .section-padding {
    padding: 6rem 0;
  }
}

/* 玻璃态效果 */
.glass {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

.glassmorphism {
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
}

/* SEO优化相关样式 */
@media (prefers-reduced-motion: reduce) {
  .animate-float,
  .floating-element,
  .pulse-glow,
  .rotate-slow,
  .slide-up-fade-in,
  .timeline-pulse {
    animation: none !important;
  }
  
  .shine::after {
    display: none !important;
  }
  
  html {
    scroll-behavior: auto !important;
  }
}
