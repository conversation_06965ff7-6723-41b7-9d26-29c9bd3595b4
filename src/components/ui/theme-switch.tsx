import React from 'react';
import { useTheme } from '@/lib/theme-context';
import { Sun, Moon, Laptop, Palette } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ThemeSwitchProps {
  className?: string;
}

export function ThemeSwitch({ className }: ThemeSwitchProps) {
  const { theme, setTheme } = useTheme();

  return (
    <div className={cn('flex items-center gap-2 rounded-full p-1 bg-secondary', className)}>
      <button
        onClick={() => setTheme('light')}
        className={cn(
          'p-2 rounded-full transition-all',
          theme === 'light' ? 'bg-white text-primary shadow-sm' : 'hover:bg-white/50'
        )}
        aria-label="Light mode"
      >
        <Sun className="h-4 w-4" />
      </button>
      
      {/* <button
        onClick={() => setTheme('dark')}
        className={cn(
          'p-2 rounded-full transition-all',
          theme === 'dark' ? 'bg-white text-primary shadow-sm' : 'hover:bg-white/50'
        )}
        aria-label="Dark mode"
      >
        <Moon className="h-4 w-4" />
      </button> */}
      
      <button
        onClick={() => setTheme('wenhao')}
        className={cn(
          'p-2 rounded-full transition-all',
          theme === 'wenhao' ? 'bg-white text-primary shadow-sm' : 'hover:bg-white/50'
        )}
        aria-label="文浩主题"
      >
        <Palette className="h-4 w-4" />
      </button>
      
      {/* <button
        onClick={() => setTheme('system')}
        className={cn(
          'p-2 rounded-full transition-all',
          theme === 'system' ? 'bg-white text-primary shadow-sm' : 'hover:bg-white/50'
        )}
        aria-label="System theme"
      >
        <Laptop className="h-4 w-4" />
      </button> */}
    </div>
  );
} 