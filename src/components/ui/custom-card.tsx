
import React from 'react';
import { cn } from '@/lib/utils';
import { Link } from 'react-router-dom';

interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  href?: string;
  isExternal?: boolean;
  className?: string;
  hoverEffect?: boolean;
}

export const Card = ({
  href,
  isExternal = false,
  className,
  hoverEffect = true,
  children,
  ...props
}: CardProps) => {
  const cardClasses = cn(
    'bg-white/70 backdrop-blur-sm border border-border rounded-xl overflow-hidden transition-all duration-300 subtle-shadow',
    hoverEffect && 'hover-scale card-highlight',
    className
  );

  if (href) {
    if (isExternal) {
      return (
        <a 
          href={href} 
          target="_blank" 
          rel="noopener noreferrer" 
          className={cardClasses}
          {...(props as unknown as React.AnchorHTMLAttributes<HTMLAnchorElement>)}
        >
          {children}
        </a>
      );
    }
    
    return (
      <Link to={href} className={cardClasses} {...(props as any)}>
        {children}
      </Link>
    );
  }

  return (
    <div className={cardClasses} {...props}>
      {children}
    </div>
  );
};

export const CardHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div
      className={cn('p-6 border-b border-border', className)}
      {...props}
    />
  );
};

export const CardTitle = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLHeadingElement>) => {
  return (
    <h3
      className={cn('text-xl font-medium', className)}
      {...props}
    />
  );
};

export const CardDescription = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLParagraphElement>) => {
  return (
    <p
      className={cn('text-muted-foreground text-sm', className)}
      {...props}
    />
  );
};

export const CardContent = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  return <div className={cn('p-6', className)} {...props} />;
};

export const CardFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => {
  return (
    <div
      className={cn('p-6 pt-0', className)}
      {...props}
    />
  );
};
