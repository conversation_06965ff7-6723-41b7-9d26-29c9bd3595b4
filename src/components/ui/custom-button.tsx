
import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { ArrowRight } from 'lucide-react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  href?: string;
  isExternal?: boolean;
  fullWidth?: boolean;
}

const Button = ({
  children,
  className,
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'right',
  href,
  isExternal = false,
  fullWidth = false,
  ...props
}: ButtonProps) => {
  const baseClasses = cn(
    'inline-flex items-center justify-center font-medium transition-all duration-300 rounded-md whitespace-nowrap',
    fullWidth && 'w-full',
    {
      'px-4 py-2 text-sm': size === 'sm',
      'px-5 py-2.5 text-base': size === 'md',
      'px-6 py-3 text-lg': size === 'lg',
    },
    {
      'bg-primary text-primary-foreground hover:bg-primary/90': variant === 'primary',
      'bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',
      'border border-border bg-transparent hover:bg-secondary/50': variant === 'outline',
      'bg-transparent hover:bg-secondary': variant === 'ghost',
    },
    className
  );

  const content = (
    <>
      {icon && iconPosition === 'left' && <span className="mr-2">{icon}</span>}
      {children}
      {icon && iconPosition === 'right' && <span className="ml-2">{icon}</span>}
    </>
  );

  if (href) {
    if (isExternal) {
      return (
        <a
          href={href}
          className={baseClasses}
          target="_blank"
          rel="noopener noreferrer"
        >
          {content}
        </a>
      );
    }
    return (
      <Link to={href} className={baseClasses}>
        {content}
      </Link>
    );
  }

  return (
    <button className={baseClasses} {...props}>
      {content}
    </button>
  );
};

export const ArrowButton: React.FC<ButtonProps> = (props) => {
  return <Button {...props} icon={<ArrowRight className="h-4 w-4" />} />;
};

export default Button;
