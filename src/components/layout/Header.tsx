import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, Target, X, ExternalLink } from 'lucide-react';
import { ThemeSwitch } from '../ui/theme-switch';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const navLinks = [
    { name: '首页', path: '/' },
    // { name: '博客', path: 'https://blog.wenhaofree.com', target: '_blank'},
    { name: '博客', path: 'https://blog.fuwenhao.club?utm_source=fuwenhao.club', target: '_blank'},
    // { name: '文章', path: '/blog' },
    { name: '项目', path: '/projects' },
    // { name: '关于', path: '/#about' },
    { name: '联系', path: '/#contact' },
  ];

  const isActive = (path: string) => {
    if (path.startsWith('/#')) {
      return location.pathname === '/' && location.hash === path.substring(1);
    }
    return location.pathname === path;
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? 'glass py-3 backdrop-blur-xl bg-white/80 dark:bg-gray-900/80' 
          : 'bg-transparent py-5'
      }`}
    >
      <div className="container mx-auto px-4 flex items-center justify-between">
        <Link to="/" className="text-2xl font-display font-bold text-gradient relative group flex items-center">
          <img src="/logo.png" alt="文浩free的个人网站" className="h-8 mr-2" />
          文浩Free
          <span className="gradient-underline"></span>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-8">
          <nav className="flex items-center space-x-8">
            {navLinks.map((link) => {
              // 外部链接使用a标签
              if (link.path.startsWith('http')) {
                return (
                  <a
                    key={link.name}
                    href={link.path}
                    target={link.target}
                    rel="noopener noreferrer"
                    className={`font-medium transition-all duration-300 relative text-foreground/80 hover:text-primary link-underline`}
                  >
                    <div className="flex items-center">
                      {link.name}
                      {link.target === '_blank' && <ExternalLink className="ml-1 h-3 w-3" />}
                    </div>
                  </a>
                );
              }
              
              // 内部链接使用Link组件
              return (
                <Link
                  key={link.name}
                  to={link.path}
                  className={`font-medium transition-all duration-300 relative ${
                    isActive(link.path)
                      ? 'text-primary after:absolute after:bottom-0 after:left-0 after:h-0.5 after:w-full after:bg-primary'
                      : 'text-foreground/80 hover:text-primary link-underline'
                  }`}
                >
                  {link.name}
                </Link>
              );
            })}
          </nav>
          
          <ThemeSwitch />
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden flex items-center space-x-4">
          <ThemeSwitch className="scale-75" />
          
          <button
            className="text-foreground focus:outline-none"
            onClick={toggleMobileMenu}
            aria-label="Toggle mobile menu"
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMobileMenuOpen && (
        <div className="md:hidden glass animate-fade-in absolute top-full left-0 right-0 p-4 border-t border-white/10">
          <nav className="flex flex-col space-y-4">
            {navLinks.map((link) => {
              // 外部链接使用a标签
              if (link.path.startsWith('http')) {
                return (
                  <a
                    key={link.name}
                    href={link.path}
                    target={link.target}
                    rel="noopener noreferrer"
                    className={`font-medium p-2 transition-all duration-300 text-foreground/80 hover:text-primary hover:bg-white/30 rounded-md dark:hover:bg-gray-800/30`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {link.name}
                  </a>
                );
              }
              
              // 内部链接使用Link组件
              return (
                <Link
                  key={link.name}
                  to={link.path}
                  className={`font-medium p-2 transition-all duration-300 ${
                    isActive(link.path)
                      ? 'text-primary bg-white/50 rounded-md dark:bg-gray-800/50'
                      : 'text-foreground/80 hover:text-primary hover:bg-white/30 rounded-md dark:hover:bg-gray-800/30'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {link.name}
                </Link>
              );
            })}
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;
