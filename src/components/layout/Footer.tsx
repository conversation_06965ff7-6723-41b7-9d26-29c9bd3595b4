
import React from 'react';
import { Link } from 'react-router-dom';
import { Github, Twitter, Linkedin, Mail, Heart, ExternalLink, ArrowUp } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  const socialLinks = [
    { icon: Github, href: 'https://github.com/wenhaofree', label: 'GitHub' },
    { icon: Twitter, href: 'https://twitter.com/wenhaofree', label: 'Twitter' },
    // { icon: Linkedin, href: 'https://linkedin.com/', label: 'LinkedIn' },
    { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email' },
  ];

  const footerLinks = [
    { name: '首页', path: '/' },
    // { name: '博客', path: 'https://blog.wenhaofree.com', target: '_blank'},
    { name: '博客', path: 'https://blog.fuwenhao.club?utm_source=fuwenhao.club', target: '_blank'},
    { name: '项目', path: '/projects' },
    // { name: '关于', path: '/#about' },
    { name: '联系', path: '/#contact' },
  ];

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <footer className="bg-secondary/50 border-t border-border pt-16 mt-20 dark:bg-gray-900/50 relative">
      {/* Back to top button */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        <button 
          onClick={scrollToTop} 
          className="bg-primary hover:bg-primary/90 text-white p-4 rounded-full shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-1"
          aria-label="回到顶部"
        >
          <ArrowUp className="h-5 w-5" />
        </button>
      </div>
      
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div className="space-y-4 md:col-span-2">
            <Link to="/" className="text-2xl font-display font-bold text-gradient">
              文浩
            </Link>
            <p className="text-muted-foreground max-w-xs">
              个人博客与作品集展示平台，分享我的技术见解和创意项目。专注于前端开发、用户体验与创新技术。
            </p>
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <a
                  key={social.label}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-transform duration-300 hover:scale-110"
                  aria-label={social.label}
                >
                  <social.icon className="h-5 w-5" />
                </a>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="font-medium text-lg">快速链接</h3>
            <nav className="flex flex-col space-y-2">
              {footerLinks.map((link) => {
                // Check if the link is external (starts with http:// or https://)
                const isExternal = link.path.startsWith('http');
                
                return isExternal ? (
                  <a
                    key={link.name}
                    href={link.path}
                    target={link.target || '_self'}
                    rel={link.target === '_blank' ? 'noopener noreferrer' : ''}
                    className="text-muted-foreground hover:text-primary transition-all duration-200 hover:translate-x-1 flex items-center"
                  >
                    <span>{link.name}</span>
                    {link.target === '_blank' && <ExternalLink className="ml-1 h-3 w-3" />}
                  </a>
                ) : (
                  <Link
                    key={link.name}
                    to={link.path}
                    className="text-muted-foreground hover:text-primary transition-all duration-200 hover:translate-x-1 flex items-center"
                  >
                    <span>{link.name}</span>
                  </Link>
                );
              })}
            </nav>
          </div>

          <div className="space-y-4">
            <h3 className="font-medium text-lg">订阅更新</h3>
            <p className="text-muted-foreground">
              第一时间获取最新的博客文章和项目更新。
            </p>
            <div className="flex mt-2">
              <input
                type="email"
                placeholder="您的邮箱"
                className="px-4 py-2 w-full bg-background border border-border rounded-l-md focus:outline-none focus:ring-1 focus:ring-primary transition-all duration-300"
              />
              <button className="px-4 py-2 bg-primary text-white rounded-r-md hover:bg-primary/90 transition-all duration-300 flex items-center">
                订阅
                <ExternalLink className="ml-1 h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        <div className="border-t border-border mt-12 pt-8 pb-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-muted-foreground text-sm">
            © 2023 - {currentYear} 文浩个人作品集. 保留所有权利。
          </p>
          {/* <div className="flex items-center text-muted-foreground text-sm mt-4 md:mt-0">
            <span>使用</span>
            <span className="mx-1 inline-flex items-center text-primary">
              <Heart className="h-3 w-3 mr-1 animate-pulse" /> 
              React + Tailwind
            </span>
            <span>精心制作</span>
          </div> */}
        </div>
      </div>
    </footer>
  );
};

export default Footer;
