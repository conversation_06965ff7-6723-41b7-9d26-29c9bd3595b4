
import React, { useEffect, useRef } from 'react';
import { Card } from '@/components/ui/custom-card';
import { Briefcase, GraduationCap, Award, Calendar, MapPin } from 'lucide-react';

interface TimelineItem {
  id: number;
  year: string;
  title: string;
  organization: string;
  description: string;
  icon: React.FC<{ className?: string }>;
  type: 'work' | 'education' | 'achievement';
  location?: string;
}

const Timeline = () => {
  const timelineRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-slide-in');
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1, rootMargin: '0px 0px -10% 0px' }
    );
    
    const timelineItems = document.querySelectorAll('.timeline-item');
    timelineItems.forEach((item) => {
      observer.observe(item);
    });
    
    return () => {
      timelineItems.forEach((item) => {
        observer.unobserve(item);
      });
    };
  }, []);
  
  const timelineItems: TimelineItem[] = [
    {
      id: 1,
      year: '2022 - 现在',
      title: '高级前端开发工程师',
      organization: '科技创新公司',
      description: '负责公司核心产品的前端架构设计和开发，优化用户体验和性能，指导团队成员提升技术能力。',
      icon: Briefcase,
      type: 'work',
      location: '北京'
    },
    {
      id: 2,
      year: '2020 - 2022',
      title: 'Web开发工程师',
      organization: '互联网科技公司',
      description: '开发和维护企业级Web应用程序，实现响应式设计，确保跨浏览器兼容性和网站性能优化。',
      icon: Briefcase,
      type: 'work',
      location: '上海'
    },
    {
      id: 3,
      year: '2019',
      title: '计算机科学硕士学位',
      organization: '知名大学',
      description: '专注于Web开发和人工智能领域的研究，获得了校级优秀学生奖学金。',
      icon: GraduationCap,
      type: 'education',
      location: '广州'
    },
    {
      id: 4,
      year: '2018',
      title: '开发者大赛冠军',
      organization: '全国编程竞赛',
      description: '团队项目获得年度开发者挑战赛的最高奖项，项目被业界知名企业收购。',
      icon: Award,
      type: 'achievement',
    },
    {
      id: 5,
      year: '2017',
      title: '软件工程学士学位',
      organization: '计算机学院',
      description: '以优异成绩完成学业，同时参与多个校企合作项目，积累了丰富的实践经验。',
      icon: GraduationCap,
      type: 'education',
      location: '杭州'
    },
    {
      id: 6,
      year: '2016 - 2017',
      title: '开发实习生',
      organization: '初创科技企业',
      description: '参与公司产品的前端开发工作，学习实际项目中的开发流程和团队协作。',
      icon: Briefcase,
      type: 'work',
      location: '深圳'
    },
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'work':
        return 'bg-gradient-to-r from-blue-500 to-blue-700 text-white';
      case 'education':
        return 'bg-gradient-to-r from-green-500 to-green-700 text-white';
      case 'achievement':
        return 'bg-gradient-to-r from-purple-500 to-purple-700 text-white';
      default:
        return 'bg-primary text-white';
    }
  };

  const getBorderColor = (type: string) => {
    switch (type) {
      case 'work':
        return 'border-blue-500';
      case 'education':
        return 'border-green-500';
      case 'achievement':
        return 'border-purple-500';
      default:
        return 'border-primary';
    }
  };

  return (
    <section id="timeline" className="section-padding py-16 lg:py-24 bg-secondary/30">
      <div className="container mx-auto">
        <div className="text-center max-w-2xl mx-auto mb-16">
          <p className="inline-block px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4 animate-fade-in">
            成长历程
          </p>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 animate-fade-in" style={{ animationDelay: '0.1s' }}>
            职业与教育经历
          </h2>
          <p className="text-muted-foreground animate-fade-in" style={{ animationDelay: '0.2s' }}>
            我的专业发展轨迹和取得的重要成就
          </p>
        </div>

        <div className="relative" ref={timelineRef}>
          {/* Timeline center line with pulsing effect */}
          <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gradient-to-b from-primary/30 via-primary to-primary/30 rounded-full overflow-hidden">
            <div className="absolute top-0 left-0 right-0 bottom-0 bg-white/30 timeline-pulse"></div>
          </div>
          
          <div className="space-y-12 relative">
            {timelineItems.map((item, index) => (
              <div 
                key={item.id} 
                className={`relative timeline-item opacity-0 flex ${
                  index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                }`}
              >
                <div className={`w-1/2 ${index % 2 === 0 ? 'pr-12 text-right' : 'pl-12'}`}>
                  <Card className={`p-6 transform transition-all duration-300 hover:translate-y-[-5px] hover:shadow-lg shine border-t-4 ${getBorderColor(item.type)}`}>
                    <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium mb-2 ${getTypeColor(item.type)}`}>
                      {item.type === 'work' ? '工作经历' : item.type === 'education' ? '教育背景' : '获奖成就'}
                    </span>
                    <div className="mb-2 flex items-center gap-2 justify-end">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">
                        {item.year}
                      </span>
                    </div>
                    
                    <h3 className="text-xl font-medium">{item.title}</h3>
                    <p className="text-primary font-medium mt-1">{item.organization}</p>
                    
                    {item.location && (
                      <div className="flex items-center gap-1 mt-2 text-sm text-muted-foreground">
                        <MapPin className="h-3 w-3" />
                        <span>{item.location}</span>
                      </div>
                    )}
                    
                    <p className="text-muted-foreground mt-3">{item.description}</p>
                  </Card>
                </div>
                
                <div className={`absolute left-1/2 top-6 transform -translate-x-1/2 w-12 h-12 rounded-full bg-white border-4 ${getBorderColor(item.type)} flex items-center justify-center z-10 transition-all duration-300 hover:scale-110`}>
                  <item.icon className={`h-5 w-5 text-${item.type === 'work' ? 'blue' : item.type === 'education' ? 'green' : 'purple'}-500`} />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Timeline;
