import React, { useEffect, useRef, useState } from 'react';
import { ArrowButton } from '@/components/ui/custom-button';
import { ArrowDown } from 'lucide-react';

// 定义技术栈数据
const techStack = [
  { name: 'React', color: 'text-[#61DAFB]', delay: '0s' },
  { name: 'Next.js', color: 'text-black dark:text-white', delay: '0.2s' },
  { name: 'Java', color: 'text-[#f89820]', delay: '0.4s' },
  { name: 'Spring Boot', color: 'text-[#6DB33F]', delay: '0.6s' },
  { name: 'TypeScript', color: 'text-[#3178C6]', delay: '0.8s' },
  { name: 'Tailwind CSS', color: 'text-[#38B2AC]', delay: '1s' },
  { name: 'Node.js', color: 'text-[#339933]', delay: '1.2s' },
  { name: 'Docker', color: 'text-[#2496ED]', delay: '1.4s' },
  { name: 'MySQL', color: 'text-[#4479A1]', delay: '1.6s' },
  { name: 'Redis', color: 'text-[#DC382D]', delay: '1.8s' },
  { name: 'Python', color: 'text-[#3776AB]', delay: '2s' },
  { name: 'TensorFlow', color: 'text-[#FF6F00]', delay: '2.2s' }
];

const Hero = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    // 设置可见状态，用于触发初始动画
    setIsVisible(true);
    
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      
      const { left, top, width, height } = containerRef.current.getBoundingClientRect();
      const x = (e.clientX - left) / width - 0.5;
      const y = (e.clientY - top) / height - 0.5;
      
      const elements = containerRef.current.querySelectorAll('.parallax');
      elements.forEach((el) => {
        const depth = parseFloat((el as HTMLElement).dataset.depth || '0.1');
        const translateX = x * depth * 50;
        const translateY = y * depth * 50;
        (el as HTMLElement).style.transform = `translate(${translateX}px, ${translateY}px)`;
      });
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  return (
    <section 
      className="min-h-screen flex items-center justify-center relative overflow-hidden pt-20"
      ref={containerRef}
    >
      {/* 增强的浮动背景元素 */}
      <div className="absolute inset-0 z-0">
        <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-white via-blue-50 to-primary/5 dark:from-gray-900 dark:via-gray-800/50 dark:to-primary/5 wenhao:from-orange-50 wenhao:via-amber-50/50 wenhao:to-primary/5"></div>
        <div className="absolute top-1/4 left-1/3 w-96 h-96 bg-primary/10 rounded-full blur-3xl parallax pulse-glow" data-depth="0.3"></div>
        <div className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl parallax pulse-glow" data-depth="0.2" style={{animationDelay: "1.5s"}}></div>
        <div className="absolute top-1/2 right-1/3 w-64 h-64 bg-purple-500/5 rounded-full blur-3xl parallax pulse-glow" data-depth="0.15" style={{animationDelay: "0.75s"}}></div>
        
        {/* 新增的动画元素 */}
        <div className="absolute top-1/3 left-1/2 w-20 h-20 bg-green-500/20 rounded-full blur-xl parallax floating-element" data-depth="0.25" style={{animationDelay: "0.5s"}}></div>
        <div className="absolute bottom-1/4 left-1/4 w-32 h-32 bg-yellow-500/15 rounded-full blur-xl parallax floating-element" data-depth="0.18" style={{animationDelay: "1.2s"}}></div>
        
        {/* 装饰性形状 */}
        <div className="absolute top-1/4 right-1/4 w-24 h-24 bg-indigo-500/10 rounded-md rotate-45 blur-lg parallax rotate-slow" data-depth="0.22"></div>
        <div className="absolute bottom-1/3 left-1/3 w-20 h-20 bg-pink-500/10 rounded-md rotate-12 blur-lg parallax rotate-slow" data-depth="0.28" style={{animationDirection: "reverse"}}></div>
      </div>
      
      <div className="container mx-auto px-4 z-10">
        <div className={`max-w-3xl mx-auto text-center space-y-8 transition-all duration-1000 ${isVisible ? 'opacity-100' : 'opacity-0 translate-y-10'}`}>
          <p className="inline-block px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4 animate-fade-in">
            个人作品集 & 技术博客
          </p>
          <h1 className="text-5xl sm:text-6xl md:text-7xl font-bold tracking-tight animate-slide-up">
            嗨，我是 <span className="text-gradient bg-clip-text text-transparent bg-gradient-to-r from-primary to-blue-600 dark:from-primary dark:to-blue-400 wenhao:from-amber-500 wenhao:to-orange-600">文浩</span> 
            <br />用代码构建未来
          </h1>
          <p className="text-muted-foreground text-xl max-w-2xl mx-auto animate-fade-in" style={{animationDelay: "0.3s"}}>
            专注于创建优雅且高性能的网络应用程序，分享技术见解与开发心得
          </p>
          <div className="flex flex-wrap items-center justify-center gap-4 pt-6 animate-fade-in" style={{animationDelay: "0.5s"}}>
            <ArrowButton href="#projects" size="lg" className="card-pop shine">
              查看我的项目
            </ArrowButton>
            <ArrowButton href="#contact" variant="outline" size="lg" className="card-pop shine">
              联系我
            </ArrowButton>
          </div>
          
          {/* 技术栈标签云 */}
          <div className="relative h-32 mt-12 overflow-hidden">
            <div className="flex flex-wrap justify-center gap-4 animate-fade-in" style={{animationDelay: "0.6s"}}>
              {techStack.map((tech, index) => (
                <div
                  key={tech.name}
                  className={`px-4 py-2 rounded-full bg-background/80 backdrop-blur-sm border border-border 
                    hover:scale-110 transition-transform duration-300 parallax floating-tag`}
                  data-depth={0.1 + (index * 0.02)}
                  style={{
                    animationDelay: tech.delay,
                    transform: `translateY(${Math.sin(index) * 10}px)`
                  }}
                >
                  <span className={`font-medium ${tech.color}`}>{tech.name}</span>
                </div>
              ))}
            </div>
          </div>
          
          {/* 新增的装饰性元素 */}
          <div className="relative mt-16 h-16 animate-fade-in" style={{animationDelay: "0.7s"}}>
            <div className="absolute left-1/2 -translate-x-1/2 top-0 w-px h-16 bg-gradient-to-b from-transparent via-primary/30 to-primary/80"></div>
            <div className="absolute bottom-0 left-1/2 -translate-x-1/2 animate-bounce">
              <div className="p-2 rounded-full bg-background/80 backdrop-blur-sm border border-border">
                <ArrowDown className="h-5 w-5 text-primary" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
