
import React, { useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/custom-card';
import { 
  Code, 
  Layout, 
  Database, 
  Globe, 
  Smartphone, 
  Server,
  Layers,
  Cpu
} from 'lucide-react';

interface SkillCategory {
  icon: React.FC<{ className?: string }>;
  title: string;
  skills: string[];
  color: string;
}

const Skills = () => {
  const skillsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('slide-up-fade-in');
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );
    
    const cards = document.querySelectorAll('.skill-card');
    cards.forEach((card) => {
      observer.observe(card);
    });
    
    return () => {
      cards.forEach((card) => {
        observer.unobserve(card);
      });
    };
  }, []);

  const skillCategories: SkillCategory[] = [
    {
      icon: Code,
      title: '前端开发',
      skills: ['React', 'Vue', 'TypeScript', 'JavaScript', 'HTML/CSS', 'Tailwind CSS'],
      color: 'bg-gradient-to-br from-blue-400 to-blue-600'
    },
    {
      icon: Server,
      title: '后端开发',
      skills: ['Node.js', 'Express', 'Django', 'Flask', 'RESTful API', 'GraphQL'],
      color: 'bg-gradient-to-br from-green-400 to-green-600'
    },
    {
      icon: Database,
      title: '数据库',
      skills: ['MongoDB', 'PostgreSQL', 'MySQL', 'Redis', 'Firebase'],
      color: 'bg-gradient-to-br from-yellow-400 to-yellow-600'
    },
    {
      icon: Layout,
      title: 'UI/UX设计',
      skills: ['Figma', 'Adobe XD', 'Sketch', '响应式设计', '用户体验设计'],
      color: 'bg-gradient-to-br from-pink-400 to-pink-600'
    },
    {
      icon: Globe,
      title: '开发工具',
      skills: ['Git', 'Docker', 'CI/CD', 'AWS', 'Webpack', 'Vite'],
      color: 'bg-gradient-to-br from-purple-400 to-purple-600'
    },
    {
      icon: Smartphone,
      title: '移动开发',
      skills: ['React Native', 'Flutter', 'iOS', 'Android', 'PWA'],
      color: 'bg-gradient-to-br from-orange-400 to-orange-600'
    },
    {
      icon: Layers,
      title: '架构设计',
      skills: ['系统设计', '微服务', 'MVC', '前后端分离', 'API设计'],
      color: 'bg-gradient-to-br from-cyan-400 to-cyan-600'
    },
    // {
    //   icon: Cpu,
    //   title: '人工智能',
    //   skills: ['机器学习基础', 'TensorFlow', 'Python', '数据分析', 'NLP'],
    //   color: 'bg-gradient-to-br from-red-400 to-red-600'
    // }
  ];

  return (
    <section id="skills" className="section-padding bg-gradient-to-b from-white to-secondary/20" ref={skillsRef}>
      <div className="container mx-auto">
        <div className="text-center max-w-2xl mx-auto mb-16">
          <p className="inline-block px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4 slide-up-fade-in">
            我的技能
          </p>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 slide-up-fade-in stagger-1">
            技术栈与专业领域
          </h2>
          <p className="text-muted-foreground slide-up-fade-in stagger-2">
            多年来不断学习并精进的技术能力，致力于打造出高质量的数字产品与解决方案
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {skillCategories.map((category, index) => (
            <Card 
              key={category.title}
              className="skill-card opacity-0 tilt-on-hover color-card shine"
              style={{ transitionDelay: `${0.1 * index}s` }}
            >
              <CardContent className="p-6">
                <div className={`mb-4 w-12 h-12 rounded-md ${category.color} flex items-center justify-center text-white`}>
                  <category.icon className="h-6 w-6" />
                </div>
                <h3 className="text-xl font-medium mb-3">{category.title}</h3>
                <ul className="space-y-2">
                  {category.skills.map((skill) => (
                    <li key={skill} className="text-muted-foreground flex items-center group">
                      <span className="w-1.5 h-1.5 bg-primary rounded-full mr-2 group-hover:scale-150 transition-transform"></span>
                      {skill}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Skills;
