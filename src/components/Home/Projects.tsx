import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/custom-card';
import { ArrowButton } from '@/components/ui/custom-button';
import { ExternalLink, Github } from 'lucide-react';

interface Project {
  id: number;
  title: string;
  description: string;
  technologies: string[];
  image: string;
  githubUrl: string;
  demoUrl: string;
  category: 'web' | 'mobile' | 'design' | 'all';
  color: string;
}

const Projects = () => {
  const [activeFilter, setActiveFilter] = useState<'all' | 'web' | 'mobile' | 'design'>('all');
  
  const projects: Project[] = [
    {
      id: 1,
      title: '一兆资源网 - 高质量数字资源聚合平台',
      description: '一兆资源网致力于为用户提供高效、便捷的数字资源检索与收藏服务，涵盖学习、设计、开发等多个领域，是您的数字宝藏收藏夹和知识导航首选。',
      technologies: ['React', 'Node.js', 'Tailwind CSS', 'Vite'],
      image: 'https://image.wenhaofree.com/2025/06/b4ecffbff94dedaf558ecb72eebf4063.jpeg',
      githubUrl: 'https://yzzzzy.com',
      demoUrl: 'https://yzzzzy.com?utm_source=fuwenhao.club',
      category: 'web',
      color: 'from-blue-500 to-blue-700'
    },

    {
      id: 2,
      title: 'FreeHub Games - 在线小游戏合集',
      description: 'FreeHub Games 提供丰富的在线小游戏，无需下载，随时畅玩，适合所有年龄段用户。',
      technologies: ['React', 'TypeScript', 'Vite', 'Tailwind CSS'],
      image: 'https://image.wenhaofree.com/2025/06/20511244471be7ac8aaec4aa068d0f90.jpeg',
      githubUrl: 'https://github.com/wenhaofree/freehubgames',
      demoUrl: 'https://freehubgames.com/zh?utm_source=fuwenhao.club',
      category: 'web',
      color: 'from-green-500 to-green-700'
    },
    {
      id: 3,
      title: 'Aistak - AI工具导航',
      description: 'Aistak 致力于为用户发现和推荐优质的人工智能工具与软件，涵盖生产力、创意、办公等多个领域，助力高效智能工作与生活。',
      technologies: ['Python', 'DALL-E', 'Flask', 'React'],
      image: 'https://image.wenhaofree.com/2025/06/0e54731d61b2fd4ebc295746fdd624fb.png',
      githubUrl: 'https://github.com',
      demoUrl: 'https://aistak.com?utm_source=fuwenhao.club',
      category: 'web',
      color: 'from-purple-500 to-purple-700'
    },

    // {
    //   id: 1,
    //   title: '智能家居控制面板',
    //   description: '为智能家居设备打造的现代化控制面板，具有直观的用户界面和强大的自动化功能。',
    //   technologies: ['React', 'Node.js', 'WebSockets', 'Tailwind CSS'],
    //   image: 'https://images.unsplash.com/photo-1558002038-1055907df827?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    //   githubUrl: 'https://github.com',
    //   demoUrl: 'https://demo.com',
    //   category: 'web',
    //   color: 'from-blue-500 to-blue-700'
    // },
    // {
    //   id: 2,
    //   title: '健康饮食追踪APP',
    //   description: '一款移动应用程序，帮助用户追踪饮食习惯，提供营养分析和个性化的饮食建议。',
    //   technologies: ['React Native', 'Firebase', 'Redux', 'Expo'],
    //   image: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2080&q=80',
    //   githubUrl: 'https://github.com',
    //   demoUrl: 'https://demo.com',
    //   category: 'mobile',
    //   color: 'from-green-500 to-green-700'
    // },
    // {
    //   id: 3,
    //   title: '现代金融仪表板',
    //   description: '为金融分析师设计的仪表板界面，展示实时市场数据和投资组合绩效指标。',
    //   technologies: ['Vue.js', 'D3.js', 'Express', 'PostgreSQL'],
    //   image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
    //   githubUrl: 'https://github.com',
    //   demoUrl: 'https://demo.com',
    //   category: 'design',
    //   color: 'from-purple-500 to-purple-700'
    // },
    {
      id: 4,
      title: '电子商务平台',
      description: '全功能电子商务网站，具有产品管理、购物车、结账和支付集成功能。',
      technologies: ['Next.js', 'MongoDB', 'Stripe API', 'AWS'],
      image: 'https://images.unsplash.com/photo-1472851294608-062f824d29cc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      githubUrl: 'https://github.com',
      demoUrl: 'https://fuwenhao.club?utm_source=fuwenhao.club',
      category: 'web',
      color: 'from-yellow-500 to-yellow-700'
    },
    {
      id: 5,
      title: '旅游照片分享应用',
      description: '社交媒体平台，允许用户分享旅行经历和照片，包括位置标记和互动功能。',
      technologies: ['Flutter', 'Firebase', 'Google Maps API', 'GetX'],
      image: 'https://images.unsplash.com/photo-1469854523086-cc02fe5d8800?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2021&q=80',
      githubUrl: 'https://github.com',
      demoUrl: 'https://fuwenhao.club?utm_source=fuwenhao.club',
      category: 'mobile',
      color: 'from-pink-500 to-pink-700'
    },
    {
      id: 6,
      title: '人工智能新闻聚合器',
      description: '使用AI算法个性化推送新闻内容，根据用户阅读习惯和兴趣定制新闻摘要。',
      technologies: ['Python', 'TensorFlow', 'Flask', 'React'],
      image: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      githubUrl: 'https://github.com',
      demoUrl: 'https://fuwenhao.club?utm_source=fuwenhao.club',
      category: 'web',
      color: 'from-red-500 to-red-700'
    }
  ];
  
  const filteredProjects = activeFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);
  
  const categories = [
    { value: 'all', label: '全部' },
    { value: 'web', label: '网站开发' },
    { value: 'mobile', label: '移动应用' },
    { value: 'design', label: '设计项目' }
  ];

  return (
    <section id="projects" className="section-padding">
      <div className="container mx-auto">
        <div className="text-center max-w-2xl mx-auto mb-16">
          <p className="inline-block px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4 animate-fade-in">
            我的项目
          </p>
          <h2 className="text-3xl md:text-4xl font-bold mb-6 animate-fade-in" style={{ animationDelay: '0.1s' }}>
            近期作品展示
          </h2>
          <p className="text-muted-foreground animate-fade-in" style={{ animationDelay: '0.2s' }}>
            精选项目和作品案例，展示我的技术能力和创造力
          </p>
          
          <div className="flex flex-wrap justify-center gap-2 mt-8">
            {categories.map(category => (
              <button
                key={category.value}
                onClick={() => setActiveFilter(category.value as any)}
                className={`px-4 py-2 rounded-full transition-all ${
                  activeFilter === category.value
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-secondary hover:bg-secondary/80'
                }`}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredProjects.map((project, index) => (
            <Card 
              key={project.id}
              className="animate-fade-in color-card overflow-hidden shine"
              style={{ animationDelay: `${0.1 * (index + 1)}s` }}
            >
              <div className="relative aspect-video w-full overflow-hidden group">
                <div className={`absolute inset-0 bg-gradient-to-br ${project.color} opacity-10 group-hover:opacity-20 transition-opacity duration-300`}></div>
                <img 
                  src={project.image} 
                  alt={project.title} 
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end">
                  <div className="p-4 text-white">
                    <h4 className="font-medium">{project.title}</h4>
                    <p className="text-sm text-white/80">{project.category === 'web' ? '网站开发' : project.category === 'mobile' ? '移动应用' : '设计项目'}</p>
                  </div>
                </div>
              </div>
              <CardContent className="p-6">
                <h3 className="text-xl font-medium mb-2">
                  <a
                    href={project.demoUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="hover:underline"
                  >
                    {project.title}
                  </a>
                </h3>
                <p className="text-muted-foreground text-sm mb-4">{project.description}</p>
                
                <div className="flex flex-wrap gap-2 mb-6">
                  {project.technologies.map(tech => (
                    <span 
                      key={tech} 
                      className="px-2 py-1 bg-secondary text-xs rounded-full hover:bg-secondary/80 transition-colors duration-300"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
                
                <div className="flex justify-between items-center">
                  <div className="flex space-x-3">
                    <a 
                      href={project.githubUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary transition-colors"
                      aria-label="GitHub repository"
                    >
                      <Github className="h-5 w-5" />
                    </a>
                    <a 
                      href={project.demoUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary transition-colors"
                      aria-label="Live demo"
                    >
                      <ExternalLink className="h-5 w-5" />
                    </a>
                  </div>
                  <ArrowButton variant="ghost" href={project.demoUrl} size="sm" isExternal>
                    详情
                  </ArrowButton>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <ArrowButton href="/projects" size="lg" className="card-pop">
            查看全部项目
          </ArrowButton>
        </div>
      </div>
    </section>
  );
};

export default Projects;
