import { Helmet } from 'react-helmet-async';
import React from 'react';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  noindex?: boolean;
}

const SEO: React.FC<SEOProps> = ({
  title = '文浩Free - 个人作品集与技术博客 | WenhaoFree',
  description = '文浩Free (WenhaoFree) 的个人网站，展示前端开发作品集、技术博客和开源项目。专注于React、Vue、TypeScript等前沿技术。',
  keywords = 'wenhaofree, 文浩free, 文浩Marvin, 前端开发, web开发, React, Vue, 个人作品集, 技术博客',
  image = '/og-image.png',
  url = 'https://wenhaofree.com/',
  type = 'website',
  noindex = false,
}) => {
  const siteTitle = '文浩Free | WenhaoFree';
  const fullTitle = title === siteTitle ? title : `${title} | ${siteTitle}`;
  
  return (
    <Helmet>
      {/* 基本元标签 */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      {noindex && <meta name="robots" content="noindex, nofollow" />}
      <link rel="canonical" href={url} />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={url} />
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:url" content={url} />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
    </Helmet>
  );
};

export default SEO;
