@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 204 100% 40%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 204 100% 40%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 1rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 204 100% 40%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 204 100% 40%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }

  /* 文浩主题 - 温暖橙黄色调为主 */
  .wenhao {
    --background: 40 30% 98%;
    --foreground: 20 80% 10%;
    --card: 0 0% 100%;
    --card-foreground: 20 80% 10%;
    --popover: 0 0% 100%;
    --popover-foreground: 20 80% 10%;
    --primary: 30 95% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 40 20% 92%;
    --secondary-foreground: 20 80% 10%;
    --muted: 40 10% 90%;
    --muted-foreground: 25 40% 30%;
    --accent: 30 95% 50%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 85% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 40 10% 85%;
    --input: 40 10% 85%;
    --ring: 30 95% 50%;
  }

  * {
    @apply border-border;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground antialiased overflow-x-hidden;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }
}

@layer components {
  /* 文浩主题特有组件样式 */
  .wenhao header.glass {
    @apply bg-orange-50/80;
  }
  
  .wenhao .text-primary {
    @apply text-amber-500;
  }
  
  .wenhao .bg-primary {
    @apply bg-amber-500;
  }
  
  /* 修复循环依赖 */
  .wenhao header span.gradient-underline {
    background-image: linear-gradient(to right, #f59e0b, #ea580c);
  }
  
  .wenhao nav a.bg-white\/50 {
    @apply bg-orange-100/50;
  }
  
  .wenhao nav a:hover.bg-white\/30 {
    @apply hover:bg-orange-100/30;
  }
  
  .wenhao footer {
    @apply bg-orange-50/50;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .glass {
    @apply backdrop-blur-xl bg-white/70 border border-white/20 shadow-sm dark:bg-black/40 dark:border-white/10;
  }
  
  .wenhao .glass {
    @apply backdrop-blur-xl bg-orange-50/60 border-orange-200/30;
  }
  
  .glassmorphism {
    @apply backdrop-blur-md bg-white/10 border border-white/20 dark:bg-black/20 dark:border-white/10;
  }
  
  .wenhao .glassmorphism {
    @apply backdrop-blur-md bg-orange-100/10 border-orange-200/20;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-blue-500 bg-clip-text text-transparent dark:from-primary dark:to-blue-400;
  }
  
  .wenhao .text-gradient {
    background-image: linear-gradient(to right, #f59e0b, #ea580c);
    @apply bg-clip-text text-transparent;
  }
  
  /* 添加新工具类避免循环依赖 */
  .gradient-underline {
    @apply absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary to-blue-500 transition-all duration-300 group-hover:w-full dark:from-primary dark:to-blue-400;
  }
  
  .subtle-shadow {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  }
  
  .hover-scale {
    @apply transition-transform duration-300 hover:scale-[1.02];
  }
  
  .card-highlight {
    @apply relative overflow-hidden;
  }
  
  .card-highlight::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-tr from-primary/10 to-transparent opacity-0 transition-opacity duration-300;
  }
  
  .card-highlight:hover::before {
    @apply opacity-100;
  }
  
  .section-padding {
    @apply py-20 px-4 sm:px-6 md:px-8 lg:px-12;
  }
  
  .link-underline {
    @apply relative after:absolute after:bottom-0 after:left-0 after:h-[2px] after:w-full after:origin-bottom-right after:scale-x-0 after:bg-primary after:transition-transform after:duration-300 hover:after:origin-bottom-left hover:after:scale-x-100;
  }
  
  /* 增强的动画效果 */
  .float-slow {
    animation: float-slow 6s ease-in-out infinite;
  }
  
  .float-medium {
    animation: float-slow 4s ease-in-out infinite;
  }
  
  .tilt-on-hover {
    transition: transform 0.3s ease;
  }
  
  .tilt-on-hover:hover {
    transform: perspective(1000px) rotateX(2deg) rotateY(2deg);
  }
  
  .card-pop {
    @apply transition-all duration-300;
  }
  
  .card-pop:hover {
    @apply -translate-y-2 shadow-lg;
  }
  
  .slide-up-fade-in {
    opacity: 0;
    transform: translateY(20px);
    animation: slideUpFadeIn 0.7s ease forwards;
  }
  
  /* 交错动画延迟 */
  .stagger-1 { animation-delay: 0.1s; }
  .stagger-2 { animation-delay: 0.2s; }
  .stagger-3 { animation-delay: 0.3s; }
  .stagger-4 { animation-delay: 0.4s; }
  .stagger-5 { animation-delay: 0.5s; }
  
  /* 色彩卡片悬停效果 */
  .color-card {
    @apply relative overflow-hidden transform transition-all duration-300 ease-in-out;
  }
  
  .color-card:hover {
    @apply shadow-lg -translate-y-1;
  }
  
  .color-card::after {
    content: '';
    @apply absolute bottom-0 left-0 w-full h-1 bg-primary transform scale-x-0 origin-left transition-transform duration-300;
  }
  
  .color-card:hover::after {
    @apply scale-x-100;
  }
  
  /* 闪光效果 */
  .shine {
    position: relative;
    overflow: hidden;
  }
  
  .shine::before {
    content: '';
    position: absolute;
    top: 0;
    left: -75%;
    width: 50%;
    height: 100%;
    background: linear-gradient(
      to right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.3) 100%
    );
    transform: skewX(-25deg);
    transition: all 0.75s;
  }
  
  .shine:hover::before {
    animation: shine 1.5s;
  }
  
  /* 背景纹理 */
  .bg-gradient-dots {
    background-image: radial-gradient(rgba(0, 0, 0, 0.05) 1px, transparent 0);
    background-size: 20px 20px;
  }
  
  /* Hero 新增动画 */
  .pulse-glow {
    animation: pulse-glow 3s ease-in-out infinite;
  }
  
  .rotate-slow {
    animation: rotate-slow 20s linear infinite;
  }
  
  .floating-elements > * {
    position: absolute;
    border-radius: 50%;
    opacity: 0.2;
    filter: blur(8px);
  }
  
  .floating-element-1 {
    top: 20%;
    left: 10%;
    width: 60px;
    height: 60px;
    background: var(--primary);
    animation: float-element 15s ease-in-out infinite;
  }
  
  .floating-element-2 {
    top: 60%;
    left: 80%;
    width: 80px;
    height: 80px;
    background: var(--accent);
    animation: float-element 18s ease-in-out infinite reverse;
  }
  
  .floating-element-3 {
    top: 30%;
    left: 60%;
    width: 40px;
    height: 40px;
    background: var(--primary);
    animation: float-element 12s ease-in-out infinite 2s;
  }
  
  /* 动画关键帧 */
  @keyframes float-slow {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
  }
  
  @keyframes slideUpFadeIn {
    0% { opacity: 0; transform: translateY(20px); }
    100% { opacity: 1; transform: translateY(0); }
  }
  
  @keyframes shine {
    100% {
      left: 125%;
    }
  }
  
  @keyframes pulse-glow {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.05); }
  }
  
  @keyframes rotate-slow {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  @keyframes float-element {
    0%, 100% { transform: translate(0, 0); }
    25% { transform: translate(20px, -15px); }
    50% { transform: translate(0, -30px); }
    75% { transform: translate(-20px, -15px); }
  }

  /* 技术栈标签动画 */
  .floating-tag {
    animation: float-tag 3s ease-in-out infinite;
    animation-direction: alternate;
    transition: all 0.3s ease;
  }

  .floating-tag:hover {
    transform: scale(1.1) translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }

  @keyframes float-tag {
    0% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
    100% {
      transform: translateY(0px);
    }
  }

  /* 确保动画延迟交错效果 */
  .floating-tag:nth-child(2n) {
    animation-delay: 0.2s;
  }

  .floating-tag:nth-child(3n) {
    animation-delay: 0.4s;
  }

  .floating-tag:nth-child(4n) {
    animation-delay: 0.6s;
  }
}
