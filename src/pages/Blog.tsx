
import React, { useState, useEffect } from 'react';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import { Card, CardContent } from '@/components/ui/custom-card';
import { ArrowButton } from '@/components/ui/custom-button';
import { Calendar, Clock, Tag, ChevronRight, Search } from 'lucide-react';

interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  date: string;
  readTime: string;
  image: string;
  category: string;
  tags: string[];
}

const Blog = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  
  // Simulate fetch blog posts
  const blogPosts: BlogPost[] = [
    {
      id: 1,
      title: '如何在React中实现高性能动画效果',
      excerpt: '探索在React应用中创建流畅、高性能动画的最佳实践和技巧，包括Framer Motion和CSS动画的应用。',
      date: '2023-05-15',
      readTime: '8 min',
      image: 'https://images.unsplash.com/photo-1555099962-4199c345e5dd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      category: '前端开发',
      tags: ['React', '动画', '性能优化']
    },
    {
      id: 2,
      title: '使用TypeScript提升代码质量的10个技巧',
      excerpt: '深入了解TypeScript如何帮助您编写更安全、更可维护的代码，以及避免常见陷阱的实用技巧。',
      date: '2023-04-20',
      readTime: '12 min',
      image: 'https://images.unsplash.com/photo-1579403124614-197f69d8187b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2064&q=80',
      category: '编程语言',
      tags: ['TypeScript', '代码质量', '最佳实践']
    },
    {
      id: 3,
      title: '现代Web应用中的状态管理解决方案比较',
      excerpt: '比较Redux、MobX、Zustand等流行状态管理库的优缺点，帮助您为项目选择最合适的解决方案。',
      date: '2023-03-10',
      readTime: '15 min',
      image: 'https://images.unsplash.com/photo-1485856407642-7f9ba0268b51?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      category: '前端开发',
      tags: ['状态管理', 'Redux', 'React']
    },
    {
      id: 4,
      title: '构建可访问性良好的Web表单的完整指南',
      excerpt: '学习如何设计和开发既美观又符合WCAG标准的表单，提升所有用户的使用体验。',
      date: '2023-02-28',
      readTime: '10 min',
      image: 'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      category: 'UI/UX设计',
      tags: ['可访问性', '表单设计', 'HTML']
    },
    {
      id: 5,
      title: 'Node.js微服务架构：实战经验分享',
      excerpt: '基于实际项目经验，分享在Node.js环境中设计和实现微服务架构的策略和教训。',
      date: '2023-02-15',
      readTime: '18 min',
      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2026&q=80',
      category: '后端开发',
      tags: ['Node.js', '微服务', '系统架构']
    },
    {
      id: 6,
      title: '从零开始学习WebAssembly：基础指南',
      excerpt: '探索WebAssembly的基础知识，学习如何将C++代码编译为可在浏览器中运行的高性能代码。',
      date: '2023-01-10',
      readTime: '20 min',
      image: 'https://images.unsplash.com/photo-1478860409698-8707f313ee8b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      category: '编程语言',
      tags: ['WebAssembly', 'C++', '性能优化']
    }
  ];
  
  // Extract unique categories
  const categories = ['all', ...new Set(blogPosts.map(post => post.category))];
  
  // Filter posts based on search and category
  const filteredPosts = blogPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
                          post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = activeCategory === 'all' || post.category === activeCategory;
    
    return matchesSearch && matchesCategory;
  });
  
  // Format date
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('zh-CN', options);
  };
  
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-grow pt-20">
        {/* Hero Section */}
        <section className="bg-secondary/30 py-20">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 animate-fade-in">技术文章</h1>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto mb-8 animate-fade-in" style={{ animationDelay: '0.1s' }}>
              分享我对Web开发、设计和技术趋势的见解、教程和最佳实践
            </p>
            
            <div className="max-w-md mx-auto animate-fade-in" style={{ animationDelay: '0.2s' }}>
              <div className="relative">
                <input
                  type="text"
                  placeholder="搜索文章..."
                  className="w-full px-4 py-3 pr-10 rounded-full border border-border focus:outline-none focus:ring-1 focus:ring-primary"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
              </div>
            </div>
          </div>
        </section>
        
        {/* Blog Content */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="flex flex-wrap justify-center gap-2 mb-12">
              {categories.map(category => (
                <button
                  key={category}
                  onClick={() => setActiveCategory(category)}
                  className={`px-4 py-2 rounded-full transition-all ${
                    activeCategory === category
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-secondary hover:bg-secondary/80'
                  }`}
                >
                  {category === 'all' ? '全部分类' : category}
                </button>
              ))}
            </div>
            
            {filteredPosts.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredPosts.map((post) => (
                  <Card 
                    key={post.id}
                    href={`/blog/${post.id}`}
                    className="h-full flex flex-col"
                  >
                    <div className="relative aspect-video w-full overflow-hidden">
                      <img 
                        src={post.image} 
                        alt={post.title} 
                        className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="inline-block px-3 py-1 bg-primary/80 backdrop-blur-sm text-white text-xs rounded-full">
                          {post.category}
                        </span>
                      </div>
                    </div>
                    
                    <CardContent className="p-6 flex-grow flex flex-col">
                      <div className="flex items-center text-muted-foreground text-sm mb-3">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span className="mr-4">{formatDate(post.date)}</span>
                        <Clock className="h-4 w-4 mr-1" />
                        <span>{post.readTime}</span>
                      </div>
                      
                      <h2 className="text-xl font-medium mb-3 line-clamp-2">{post.title}</h2>
                      <p className="text-muted-foreground mb-4 flex-grow line-clamp-3">{post.excerpt}</p>
                      
                      <div className="flex flex-wrap gap-2 mb-4">
                        {post.tags.map(tag => (
                          <span 
                            key={tag} 
                            className="inline-flex items-center px-2 py-1 bg-secondary text-xs rounded-full"
                          >
                            <Tag className="h-3 w-3 mr-1" /> {tag}
                          </span>
                        ))}
                      </div>
                      
                      <div className="flex justify-end">
                        <ArrowButton 
                          variant="ghost" 
                          href={`/blog/${post.id}`} 
                          size="sm"
                        >
                          阅读全文
                        </ArrowButton>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-20">
                <p className="text-xl text-muted-foreground">没有找到符合条件的文章</p>
                <button 
                  onClick={() => { setSearchQuery(''); setActiveCategory('all'); }}
                  className="mt-4 text-primary font-medium"
                >
                  清除筛选条件
                </button>
              </div>
            )}
            
            {/* Pagination */}
            {filteredPosts.length > 0 && (
              <div className="flex justify-center mt-12">
                <div className="flex items-center space-x-2">
                  <button className="w-10 h-10 flex items-center justify-center rounded-md border border-border">
                    <ChevronRight className="h-5 w-5 transform rotate-180" />
                  </button>
                  <button className="w-10 h-10 flex items-center justify-center rounded-md bg-primary text-white">
                    1
                  </button>
                  <button className="w-10 h-10 flex items-center justify-center rounded-md hover:bg-secondary">
                    2
                  </button>
                  <button className="w-10 h-10 flex items-center justify-center rounded-md hover:bg-secondary">
                    3
                  </button>
                  <button className="w-10 h-10 flex items-center justify-center rounded-md border border-border">
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </div>
              </div>
            )}
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Blog;
