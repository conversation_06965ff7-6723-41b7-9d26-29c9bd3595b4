
import React, { useEffect } from 'react';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import Hero from '../components/Home/Hero';
import Skills from '../components/Home/Skills';
import Projects from '../components/Home/Projects';
import Timeline from '../components/Home/Timeline';
import Contact from '../components/Home/Contact';
import { SplashCursor } from "@/components/ui/splash-cursor"
import SEO from '../components/SEO';

const Index = () => {
  useEffect(() => {
    // Scroll to section based on URL hash
    if (window.location.hash) {
      const id = window.location.hash.substring(1);
      const element = document.getElementById(id);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      window.scrollTo(0, 0);
    }

    // 优化的滚动动画
    const animateOnScroll = () => {
      const elements = document.querySelectorAll('.animate-on-scroll');
      elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementHeight = element.getBoundingClientRect().height;
        const windowHeight = window.innerHeight;
        
        if (elementTop < windowHeight - elementHeight / 4) {
          element.classList.add('slide-up-fade-in');
        }
      });
    };

    // 添加一个进度条在页面顶部
    const updateScrollProgress = () => {
      const scrollProgress = document.getElementById('scroll-progress');
      if (scrollProgress) {
        const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
        const scrollHeight = document.documentElement.scrollHeight - document.documentElement.clientHeight;
        const scrollPercent = (scrollTop / scrollHeight) * 100;
        scrollProgress.style.width = scrollPercent + '%';
      }
    };

    window.addEventListener('scroll', animateOnScroll);
    window.addEventListener('scroll', updateScrollProgress);
    animateOnScroll(); // Initial check for elements in view
    updateScrollProgress(); // Initial progress update
    
    return () => {
      window.removeEventListener('scroll', animateOnScroll);
      window.removeEventListener('scroll', updateScrollProgress);
    };
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-dots bg-dots-md relative">
      <SEO 
        title="文浩Free - 个人作品集与技术博客"
        description="文浩Free (WenhaoFree) 的个人网站，展示前端开发作品集、技术博客和开源项目。专注于React、Vue、TypeScript等前沿技术。"
        keywords="wenhaofree, 文浩free, 文浩Marvin, 前端开发, web开发, React, Vue, 个人作品集, 技术博客"
        url="https://wenhaofree.com/"
      />
      <SplashCursor />
      
      {/* 页面顶部的滚动进度条 */}
      <div className="fixed top-0 left-0 right-0 h-1 z-50">
        <div id="scroll-progress" className="h-full bg-primary origin-left transition-all duration-100 ease-out"></div>
      </div>
      
      <Header />
      <main className="flex-grow">
        <Hero />
        
        <div id="about" className="animate-on-scroll opacity-0">
          <Skills />
        </div>
        
        <div className="animate-on-scroll opacity-0">
          <Projects />
        </div>
        
        {/* <div className="animate-on-scroll opacity-0">
          <Timeline />
        </div> */}
        
        <div  id="contact" className="animate-on-scroll opacity-0">
          <Contact />
        </div>
      </main>
      <Footer />
      
      {/* 添加额外的全局装饰元素 */}
      <div className="fixed bottom-10 right-10 opacity-70 z-10 pointer-events-none">
        <div className="w-32 h-32 rounded-full bg-gradient-to-tr from-primary/20 to-blue-500/10 blur-3xl animate-float"></div>
      </div>
      <div className="fixed top-40 left-10 opacity-50 z-10 pointer-events-none">
        <div className="w-24 h-24 rounded-full bg-gradient-to-tr from-pink-500/20 to-purple-500/10 blur-3xl animate-float" style={{animationDelay: "1.5s"}}></div>
      </div>
    </div>
  );
};

export default Index;
