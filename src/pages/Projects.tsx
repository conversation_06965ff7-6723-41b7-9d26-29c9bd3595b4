
import React, { useState, useEffect } from 'react';
import Header from '../components/layout/Header';
import Footer from '../components/layout/Footer';
import { Card, CardContent } from '@/components/ui/custom-card';
import Button, { ArrowButton } from '@/components/ui/custom-button';
import { ExternalLink, Github, Filter, Search } from 'lucide-react';
import SEO from '../components/SEO';

interface Project {
  id: number;
  title: string;
  description: string;
  longDescription: string;
  technologies: string[];
  image: string;
  githubUrl: string;
  demoUrl: string;
  category: string;
  featured: boolean;
  date: string;
}

const Projects = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  
  // Projects data
  const projects: Project[] = [
    {
      id: 1,
      title: '智能家居控制面板',
      description: '为智能家居设备打造的现代化控制面板，具有直观的用户界面和强大的自动化功能。',
      longDescription: '这个项目是一个全面的智能家居管理系统，允许用户远程控制和监控家中的智能设备。该应用程序具有响应式设计，可在任何设备上无缝运行，并提供实时数据更新和事件通知。通过集成多种设备API，用户可以创建自动化场景和定时任务，优化家居能源使用并提高生活质量。',
      technologies: ['React', 'Node.js', 'WebSockets', 'Tailwind CSS', 'MongoDB', 'Socket.io'],
      image: 'https://images.unsplash.com/photo-1558002038-1055907df827?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      githubUrl: 'https://github.com',
      demoUrl: 'https://demo.com',
      category: '网站开发',
      featured: true,
      date: '2023-06-15'
    },
    {
      id: 2,
      title: '健康饮食追踪APP',
      description: '一款移动应用程序，帮助用户追踪饮食习惯，提供营养分析和个性化的饮食建议。',
      longDescription: '这款健康饮食应用旨在帮助用户培养健康的饮食习惯。用户可以记录每日摄入的食物，获取详细的营养成分分析，包括热量、蛋白质、脂肪和碳水化合物等。应用程序使用机器学习算法分析用户的饮食偏好和健康目标，提供个性化的饮食建议和改进计划。此外，应用还包括社区功能，允许用户分享健康食谱和饮食成果。',
      technologies: ['React Native', 'Firebase', 'Redux', 'Expo', 'Google Cloud Vision API'],
      image: 'https://images.unsplash.com/photo-1546069901-ba9599a7e63c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2080&q=80',
      githubUrl: 'https://github.com',
      demoUrl: 'https://demo.com',
      category: '移动应用',
      featured: true,
      date: '2023-05-10'
    },
    {
      id: 3,
      title: '现代金融仪表板',
      description: '为金融分析师设计的仪表板界面，展示实时市场数据和投资组合绩效指标。',
      longDescription: '这个金融仪表板项目是为投资专业人士设计的，提供全面的市场数据分析和可视化。该平台集成了多个金融数据源，实时显示股票价格、市场指数和经济指标。用户可以创建和管理投资组合，追踪绩效并分析风险因素。仪表板提供强大的图表和报表功能，支持各种技术分析指标和定制化视图，帮助用户做出更明智的投资决策。',
      technologies: ['Vue.js', 'D3.js', 'Express', 'PostgreSQL', 'WebSockets', 'TradingView API'],
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      githubUrl: 'https://github.com',
      demoUrl: 'https://demo.com',
      category: '数据可视化',
      featured: true,
      date: '2023-04-20'
    },
    {
      id: 4,
      title: '电子商务平台',
      description: '全功能电子商务网站，具有产品管理、购物车、结账和支付集成功能。',
      longDescription: '这个电子商务平台是一个完整的在线购物解决方案，为商家提供了强大的产品管理系统和为客户提供了流畅的购物体验。平台功能包括详细的产品目录、高级搜索和筛选、个性化推荐系统、购物车和愿望清单功能。集成了多种支付网关，提供安全的结账流程，同时还包括订单跟踪和客户管理系统。管理员后台提供全面的销售分析和库存管理工具，帮助商家优化运营和提高销售。',
      technologies: ['Next.js', 'MongoDB', 'Stripe API', 'AWS', 'Redux', 'Elasticsearch'],
      image: 'https://images.unsplash.com/photo-1472851294608-062f824d29cc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      githubUrl: 'https://github.com',
      demoUrl: 'https://demo.com',
      category: '网站开发',
      featured: false,
      date: '2023-03-15'
    },
    {
      id: 5,
      title: '旅游照片分享应用',
      description: '社交媒体平台，允许用户分享旅行经历和照片，包括位置标记和互动功能。',
      longDescription: '这款旅游社交应用是旅行爱好者分享和发现旅游体验的理想平台。用户可以上传照片和视频，添加地理位置标签和详细的旅行日记。应用集成了地图功能，允许用户浏览特定地区的所有分享内容，发现热门景点和隐藏瑰宝。社交功能包括评论、点赞、关注其他旅行者，以及创建和分享旅行路线。高级用户还可以成为特定地区的旅行专家，提供专业建议和推荐。',
      technologies: ['Flutter', 'Firebase', 'Google Maps API', 'GetX', 'Cloud Functions'],
      image: 'https://images.unsplash.com/photo-1469854523086-cc02fe5d8800?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2021&q=80',
      githubUrl: 'https://github.com',
      demoUrl: 'https://demo.com',
      category: '移动应用',
      featured: false,
      date: '2023-02-05'
    },
    {
      id: 6,
      title: '人工智能新闻聚合器',
      description: '使用AI算法个性化推送新闻内容，根据用户阅读习惯和兴趣定制新闻摘要。',
      longDescription: '这个AI新闻聚合平台使用先进的机器学习算法来理解用户兴趣并提供个性化的新闻体验。系统从数百个可信新闻源收集内容，使用自然语言处理技术分析文章内容、主题和情感倾向。随着用户的持续使用，系统不断学习和调整推荐，提供越来越精准的内容。平台还具有关键事件提醒功能，自动生成新闻摘要，以及多语言支持，使用户能够获取全球新闻的全面视角。',
      technologies: ['Python', 'TensorFlow', 'Flask', 'React', 'NLP', 'Redis'],
      image: 'https://images.unsplash.com/photo-1504711434969-e33886168f5c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      githubUrl: 'https://github.com',
      demoUrl: 'https://demo.com',
      category: '人工智能',
      featured: false,
      date: '2023-01-20'
    },
    {
      id: 7,
      title: '音乐制作工作室',
      description: '基于Web的音乐制作工具，提供虚拟乐器、多轨录音和高级音频处理功能。',
      longDescription: '这个在线音乐制作工作室为音乐创作者提供了专业级的工具，无需安装复杂的软件。平台包括多种虚拟乐器，如合成器、鼓机和采样器，以及丰富的音效库。用户可以进行多轨录音，应用专业级的音频处理效果，如均衡器、压缩器和混响。实时协作功能允许多个音乐家同时在一个项目上工作，无论他们身在何处。项目支持导出为多种格式，并集成了分享到主流音乐平台的功能。',
      technologies: ['Web Audio API', 'React', 'Redux', 'WebRTC', 'Node.js', 'AWS S3'],
      image: 'https://images.unsplash.com/photo-1520523839897-bd0b52f945a0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80',
      githubUrl: 'https://github.com',
      demoUrl: 'https://demo.com',
      category: '音频处理',
      featured: false,
      date: '2022-12-10'
    },
    {
      id: 8,
      title: '企业资源管理系统',
      description: '为中小型企业设计的资源管理平台，整合人力资源、财务和库存管理功能。',
      longDescription: '这个企业资源管理(ERM)系统是专为中小型企业设计的全面解决方案，帮助优化业务流程和提高运营效率。系统集成了人力资源管理、财务会计、库存管理、采购和销售管理等核心业务功能。用户友好的仪表板提供业务关键指标的实时概览，支持数据驱动的决策。系统支持工作流自动化，减少手动任务并降低错误风险。模块化设计允许企业根据自身需求选择和扩展功能，确保解决方案的成本效益和可扩展性。',
      technologies: ['Angular', 'Java Spring Boot', 'MySQL', 'Docker', 'JWT', 'Jenkins'],
      image: 'https://images.unsplash.com/photo-1507925921958-8a62f3d1a50d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2076&q=80',
      githubUrl: 'https://github.com',
      demoUrl: 'https://demo.com',
      category: '企业应用',
      featured: false,
      date: '2022-11-15'
    }
  ];
  
  // Extract unique categories
  const categories = ['all', ...new Set(projects.map(project => project.category))];
  
  // Filter projects based on search and category
  const filteredProjects = projects.filter(project => {
    const matchesSearch = 
      project.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
      project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.technologies.some(tech => tech.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = activeCategory === 'all' || project.category === activeCategory;
    
    return matchesSearch && matchesCategory;
  });
  
  // Format date
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long' };
    return new Date(dateString).toLocaleDateString('zh-CN', options);
  };
  
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen flex flex-col">
      <SEO 
        title="项目展示 | 文浩Free的作品集"
        description="文浩Free (WenhaoFree) 的项目展示平台，要求展示在前端开发、移动应用和人工智能领域的作品与创新解决方案。"
        keywords="wenhaofree, 文浩free, 文浩Marvin, 前端项目, web开发作品, React项目, Vue项目"
        url="https://wenhaofree.com/projects"
      />
      <Header />
      
      <main className="flex-grow pt-20">
        {/* Hero Section */}
        <section className="bg-secondary/30 py-20">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 animate-fade-in">项目展示</h1>
            <p className="text-muted-foreground text-lg max-w-2xl mx-auto mb-8 animate-fade-in" style={{ animationDelay: '0.1s' }}>
              探索我的创意项目作品集，包含网站开发、移动应用和其他技术实践
            </p>
            
            <div className="max-w-2xl mx-auto flex flex-col sm:flex-row gap-4 animate-fade-in" style={{ animationDelay: '0.2s' }}>
              <div className="relative flex-grow">
                <input
                  type="text"
                  placeholder="搜索项目或技术..."
                  className="w-full px-4 py-3 pr-10 rounded-full border border-border focus:outline-none focus:ring-1 focus:ring-primary"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-5 w-5" />
              </div>
              
              <Button 
                variant="secondary"
                icon={<Filter className="h-5 w-5" />}
                iconPosition="left"
                onClick={() => setShowFilters(!showFilters)}
                className="sm:w-auto"
              >
                筛选
              </Button>
            </div>
            
            {showFilters && (
              <div className="flex flex-wrap justify-center gap-2 mt-6 animate-fade-in">
                {categories.map(category => (
                  <button
                    key={category}
                    onClick={() => setActiveCategory(category)}
                    className={`px-4 py-2 rounded-full transition-all ${
                      activeCategory === category
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-secondary hover:bg-secondary/80'
                    }`}
                  >
                    {category === 'all' ? '全部项目' : category}
                  </button>
                ))}
              </div>
            )}
          </div>
        </section>
        
        {/* Featured Projects */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            {filteredProjects.some(project => project.featured) && (
              <>
                <h2 className="text-2xl font-bold mb-8">精选项目</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
                  {filteredProjects
                    .filter(project => project.featured)
                    .map((project) => (
                      <Card key={project.id} className="overflow-hidden h-full">
                        <div className="grid grid-cols-1 md:grid-cols-2 h-full">
                          <div className="relative aspect-square md:aspect-auto md:h-full overflow-hidden">
                            <img 
                              src={project.image} 
                              alt={project.title} 
                              className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                            />
                            <div className="absolute top-4 left-4">
                              <span className="inline-block px-3 py-1 bg-primary/80 backdrop-blur-sm text-white text-xs rounded-full">
                                {project.category}
                              </span>
                            </div>
                          </div>
                          
                          <CardContent className="p-6 flex flex-col justify-between">
                            <div>
                              <div className="text-muted-foreground text-sm mb-2">
                                {formatDate(project.date)}
                              </div>
                              <h3 className="text-xl font-medium mb-3">{project.title}</h3>
                              <p className="text-muted-foreground mb-4">{project.description}</p>
                              
                              <div className="flex flex-wrap gap-2 mb-6">
                                {project.technologies.slice(0, 3).map(tech => (
                                  <span 
                                    key={tech} 
                                    className="px-2 py-1 bg-secondary text-xs rounded-full"
                                  >
                                    {tech}
                                  </span>
                                ))}
                                {project.technologies.length > 3 && (
                                  <span className="px-2 py-1 bg-secondary text-xs rounded-full">
                                    +{project.technologies.length - 3}
                                  </span>
                                )}
                              </div>
                            </div>
                            
                            <div className="flex justify-between items-center">
                              <div className="flex space-x-3">
                                <a 
                                  href={project.githubUrl} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-muted-foreground hover:text-primary transition-colors"
                                  aria-label="GitHub repository"
                                >
                                  <Github className="h-5 w-5" />
                                </a>
                                <a 
                                  href={project.demoUrl} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-muted-foreground hover:text-primary transition-colors"
                                  aria-label="Live demo"
                                >
                                  <ExternalLink className="h-5 w-5" />
                                </a>
                              </div>
                              <ArrowButton 
                                variant="ghost" 
                                href={`/projects/${project.id}`} 
                                size="sm"
                              >
                                详情
                              </ArrowButton>
                            </div>
                          </CardContent>
                        </div>
                      </Card>
                    ))}
                </div>
              </>
            )}
            
            {/* All Projects */}
            <h2 className="text-2xl font-bold mb-8">全部项目</h2>
            {filteredProjects.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredProjects.map((project) => (
                  <Card 
                    key={project.id}
                    className="h-full flex flex-col"
                  >
                    <div className="relative aspect-video w-full overflow-hidden">
                      <img 
                        src={project.image} 
                        alt={project.title} 
                        className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                      />
                      <div className="absolute top-4 left-4">
                        <span className="inline-block px-3 py-1 bg-primary/80 backdrop-blur-sm text-white text-xs rounded-full">
                          {project.category}
                        </span>
                      </div>
                    </div>
                    
                    <CardContent className="p-6 flex-grow flex flex-col">
                      <div className="text-muted-foreground text-sm mb-2">
                        {formatDate(project.date)}
                      </div>
                      <h3 className="text-xl font-medium mb-3">{project.title}</h3>
                      <p className="text-muted-foreground mb-4 flex-grow">{project.description}</p>
                      
                      <div className="flex flex-wrap gap-2 mb-6">
                        {project.technologies.slice(0, 3).map(tech => (
                          <span 
                            key={tech} 
                            className="px-2 py-1 bg-secondary text-xs rounded-full"
                          >
                            {tech}
                          </span>
                        ))}
                        {project.technologies.length > 3 && (
                          <span className="px-2 py-1 bg-secondary text-xs rounded-full">
                            +{project.technologies.length - 3}
                          </span>
                        )}
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <div className="flex space-x-3">
                          <a 
                            href={project.githubUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-muted-foreground hover:text-primary transition-colors"
                            aria-label="GitHub repository"
                          >
                            <Github className="h-5 w-5" />
                          </a>
                          <a 
                            href={project.demoUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="text-muted-foreground hover:text-primary transition-colors"
                            aria-label="Live demo"
                          >
                            <ExternalLink className="h-5 w-5" />
                          </a>
                        </div>
                        <ArrowButton 
                          variant="ghost" 
                          href={`/projects/${project.id}`} 
                          size="sm"
                        >
                          详情
                        </ArrowButton>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-20">
                <p className="text-xl text-muted-foreground">没有找到符合条件的项目</p>
                <button 
                  onClick={() => { setSearchQuery(''); setActiveCategory('all'); }}
                  className="mt-4 text-primary font-medium"
                >
                  清除筛选条件
                </button>
              </div>
            )}
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
};

export default Projects;
