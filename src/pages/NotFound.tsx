import { useLocation, Link } from "react-router-dom";
import { useEffect } from "react";
import Header from "../components/layout/Header";
import Footer from "../components/layout/Footer";
import { ArrowLeft } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-dots">
      <Header />
      <main className="flex-grow flex items-center justify-center">
        <div className="container mx-auto px-4 py-12 text-center">
          <div className="glass rounded-2xl p-12 max-w-lg mx-auto">
            <h1 className="text-8xl font-bold text-gradient mb-6">404</h1>
            <p className="text-xl text-muted-foreground mb-8">抱歉，您访问的页面不存在</p>
            <div className="inline-flex items-center justify-center">
              <Link 
                to="/" 
                className="flex items-center space-x-2 bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-full transition-all duration-300"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>返回首页</span>
              </Link>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default NotFound;
